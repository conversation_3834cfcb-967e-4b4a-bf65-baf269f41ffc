"use client";

import React from "react";
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator } from "@/components/ui/breadcrumb";
import { Separator } from "@/components/ui/separator";
import { SidebarTrigger, useSidebar } from "@/components/ui/sidebar";
import { WEBNAME } from "@/lib/constants";
import { NoPrefetchLink } from "@/components/ui/custom/no-prefetch-link";

export function DashHeader({ back, current }: { back?: { href: string; title: string }[]; current: { title: string } }) {
	const { isMobile } = useSidebar();
	return (
		<header className="flex h-14 shrink-0 items-center gap-2 border-b transition-[width,height] ease-linear group-has-data-[collapsible=icon]/sidebar-wrapper:h-12">
			<div className="flex items-center gap-2 px-4">
				{isMobile && (
					<>
						<SidebarTrigger className="-ml-1 shrink-0" />
						<Separator orientation="vertical" className="mr-2 h-4" />
					</>
				)}
				<Breadcrumb>
					<BreadcrumbList>
						{back &&
							back.map((item, index) => (
								<React.Fragment key={index}>
									<BreadcrumbItem className="hidden md:block">
										<BreadcrumbLink href={item.href} className="line-clamp-1">
											{item.title}
										</BreadcrumbLink>
									</BreadcrumbItem>
									<BreadcrumbSeparator className="hidden md:block" />
								</React.Fragment>
							))}
						{/* {back && (
							<>
								<BreadcrumbItem className="hidden md:block">
									<BreadcrumbLink href={back.href}>{back.title}</BreadcrumbLink>
								</BreadcrumbItem>
								<BreadcrumbSeparator className="hidden md:block" />
							</>
						)} */}
						<BreadcrumbItem>
							<BreadcrumbPage className="line-clamp-1">{current.title}</BreadcrumbPage>
						</BreadcrumbItem>
					</BreadcrumbList>
				</Breadcrumb>
			</div>
		</header>
	);
}

export function DashMobileHeader() {
	const { isMobile } = useSidebar();
	return (
		<>
			{isMobile && (
				<header className="flex h-16 shrink-0 items-center gap-2 border-b transition-[width,height] ease-linear group-has-data-[collapsible=icon]/sidebar-wrapper:h-12">
					<div className="flex items-center gap-2 px-4">
						<SidebarTrigger className="-ml-1" />
						{/* <Separator orientation="vertical" className="mr-2 h-4" /> */}

						<NoPrefetchLink href="/dashboard" className="flex flex-row items-center gap-1">
							<div className="flex aspect-square size-8 items-center justify-center rounded-lg">
								<img src="/favicon.ico" className="h-8" alt={`${WEBNAME} - AI quiz generator`} />
							</div>
							<div className="grid flex-1 text-left text-xl leading-tight">
								<span className="truncate font-semibold tracking-wide">{WEBNAME}</span>
							</div>
						</NoPrefetchLink>
					</div>
				</header>
			)}
		</>
	);
}
