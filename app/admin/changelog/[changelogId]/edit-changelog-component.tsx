"use client";

import { useEffect, useRef, useState } from "react";
import { ChevronLeft, Dot, Send } from "lucide-react";
import { toast } from "sonner";
import { SubmitButton } from "@/components/ui/custom/submit-button";
import { ofetch } from "ofetch";
import { handleError, ParamsError } from "@/@types/error";
import { cn } from "@/lib/utils";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { ScrollArea } from "@/components/ui/scroll-area";
import useAutosizeTextArea from "@/hooks/useAutosizeTextArea";
import { Separator } from "@/components/ui/separator";
import { useRouter } from "nextjs-toploader/app";
import Document from "@tiptap/extension-document";
import Paragraph from "@tiptap/extension-paragraph";
import Text from "@tiptap/extension-text";
import { Bold } from "@tiptap/extension-bold";
import BulletList from "@tiptap/extension-bullet-list";
import Heading from "@tiptap/extension-heading";
import { Link as TipTapLink } from "@tiptap/extension-link";
import ListItem from "@tiptap/extension-list-item";
import TextStyle from "@tiptap/extension-text-style";
import { Color } from "@tiptap/extension-color";
import TaskItem from "@tiptap/extension-task-item";
import TaskList from "@tiptap/extension-task-list";
import OrderedList from "@tiptap/extension-ordered-list";
import Image from "@tiptap/extension-image";
import CodeBlock from "@tiptap/extension-code-block";
import Blockquote from "@tiptap/extension-blockquote";
import { Extensions, generateJSON, JSONContent } from "@tiptap/react";
import { getChangelogStatusText } from "@/@types/admin/changelog/changelog";
import { WEBHOST } from "@/lib/constants";
import { NoPrefetchLink } from "@/components/ui/custom/no-prefetch-link";
import { SimpleEditor } from "@/components/tiptap-templates/simple/simple-editor";
import { handleImageUploadChangelog } from "@/lib/tiptap-utils";
import { ChangelogTranslation } from "@/server/db/schema.server";

export default function EditChangelogComponent({
	changelogId,
	changelogItemId,
	changelog,
}: {
	changelogId: number;
	changelogItemId: number | null | undefined;
	changelog: ChangelogTranslation | null | undefined;
}) {
	const router = useRouter();

	const [isSubmitting, setIsSubmitting] = useState(false);
	const [title, setTitle] = useState<string>("");
	const titleTextAreaRef = useRef<HTMLTextAreaElement>(null);
	useAutosizeTextArea(titleTextAreaRef.current, title);

	const [languageCode, setLanguageCode] = useState<string>("en");
	const [changelogStatus, setChangelogStatus] = useState<string>("0");
	const [changelogStatusServer, setChangelogStatusServer] = useState<string>("");

	const [html, setHtml] = useState<string>();
	const initialHtmlJson: JSONContent | undefined = changelog?.html
		? generateJSON(changelog.html, [
				Document,
				Bold,
				Heading,
				TipTapLink,
				Paragraph,
				BulletList,
				OrderedList,
				ListItem,
				TaskList,
				TaskItem,
				Text,
				TextStyle,
				Color,
				Image,
				CodeBlock,
				Blockquote,
			] as Extensions)
		: undefined;

	// Create a wrapper function that includes changelogId for image uploads
	const handleImageUploadWithChangelogId = async (
		file: File,
		onProgress?: (event: { progress: number }) => void,
		abortSignal?: AbortSignal,
	): Promise<string> => {
		return handleImageUploadChangelog(changelogId, file, onProgress, abortSignal);
	};

	useEffect(() => {
		if (changelog) {
			setTitle(changelog.title);
			setLanguageCode(changelog.lang);
			setHtml(changelog.html);
			setChangelogStatus(changelog.status.toString());
			setChangelogStatusServer(changelog.status.toString());
		}
	}, []);

	const onSubmit = async () => {
		if (isSubmitting) return;

		const titleTrim = title.trim();

		if (!titleTrim || !languageCode) {
			toast.info("Please fill in all fields");
			return;
		}

		try {
			setIsSubmitting(true);

			let requestBody: any = {
				changelogId: Number(changelogId),
				title: titleTrim,
				lang: languageCode,
				html: html,
				status: Number(changelogStatus),
			};
			if (changelogItemId) requestBody.changelogItemId = Number(changelogItemId);

			const { status, message, newChangelogId } = await ofetch("/api/admin/changelog/changelog-item", {
				method: "POST",
				body: requestBody,
			});
			handleError(status, message);
			if (changelogId) {
				setChangelogStatusServer(changelogStatus);
				toast.success("Changelog updated successfully");
				setIsSubmitting(false);
			} else {
				toast.success("Changelog added successfully");
				if (newChangelogId) {
					router.push(`/admin/changelog/${changelogId}/${newChangelogId}/`);
				}
			}
		} catch (error) {
			if (error instanceof ParamsError) {
				toast.error(error.message);
			} else {
				toast.error("An error occurred");
			}
			setIsSubmitting(false);
		}
	};

	return (
		<div className="flex h-full w-full flex-1 flex-row">
			<div className="flex h-full w-full flex-col overflow-hidden border-r">
				<div className="flex w-full flex-row items-center justify-between p-4">
					<div className="flex flex-row items-center">
						<NoPrefetchLink href="/admin/changelog" className={cn("", "flex flex-row items-center text-sm font-normal")}>
							<ChevronLeft className="mr-1 h-4 w-4" />
							Changelog
						</NoPrefetchLink>
						<p className={cn("flex flex-row items-center text-xs", changelogStatusServer === "1" ? "text-green-500" : "text-muted-foreground")}>
							<Dot />
							<span>{getChangelogStatusText(Number(changelogStatusServer))}</span>
						</p>
					</div>
					<SubmitButton isSubmitting={isSubmitting} size="sm" onClick={onSubmit}>
						<p className="flex flex-row items-center gap-1">
							<Send />
							<span>Save</span>
						</p>
					</SubmitButton>
				</div>

				<div className="mx-auto w-full max-w-3xl px-4">
					<div className="flex w-full flex-col space-y-4">
						<div className="">
							<Textarea
								ref={titleTextAreaRef}
								value={title}
								rows={1}
								className="resize-none text-2xl font-semibold shadow-none focus-visible:ring-0 focus-visible:outline-none"
								onChange={(e) => {
									setTitle(e.target.value);
									if (changelogId) return;
								}}
								placeholder="Title"
							/>
						</div>
						<Separator className="" />
					</div>
				</div>

				<div className="h-full grow overflow-hidden">
					<ScrollArea className="h-full w-full" type="always">
						<div className="mx-auto max-w-3xl px-4 py-4">
							<SimpleEditor initialHtmlJson={initialHtmlJson} onChange={setHtml} handleImageUpload={handleImageUploadWithChangelogId} />
						</div>
					</ScrollArea>
				</div>
			</div>

			<div className="h-full w-[360px] py-4 lg:w-[420px]">
				<Label className="flex-1 px-4 text-base">Changelog settings</Label>
				<div className="h-full grow overflow-hidden pb-2">
					<ScrollArea className="h-full w-full" type="always">
						<div className="my-4 space-y-6 px-4">
							{changelogId && (
								<div className="flex flex-col items-start gap-2 rounded-lg border bg-zinc-800 p-3">
									<NoPrefetchLink href={`${WEBHOST}/changelog`} target="_blank" className="text-xs underline">
										{WEBHOST}/changelog
									</NoPrefetchLink>
								</div>
							)}

							<div className="flex flex-col items-start gap-2">
								<Label>Language *</Label>
								<Select value={languageCode} onValueChange={(value) => setLanguageCode(value as string)}>
									<SelectTrigger id="framework">
										<SelectValue placeholder="Select Language">English</SelectValue>
									</SelectTrigger>
									<SelectContent position="popper">
										<SelectItem value="en" className="cursor-pointer">
											English
										</SelectItem>
									</SelectContent>
								</Select>
							</div>
							{/* <div className="flex flex-col items-start gap-2">
								<Label className="">Featured Image</Label>
								<Input value={title || ""} onChange={(e) => setTitle(e.target.value)} placeholder="Image URL" className="shrink-0" />
							</div> */}
							<div className="flex flex-col items-start gap-2">
								<Label>Status</Label>
								<Select value={changelogStatus} onValueChange={(value) => setChangelogStatus(value as string)}>
									<SelectTrigger>
										<SelectValue placeholder="Select Category">{getChangelogStatusText(Number(changelogStatus))}</SelectValue>
									</SelectTrigger>
									<SelectContent position="popper">
										<SelectItem value="0" className="cursor-pointer">
											Draft
										</SelectItem>
										<SelectItem value="1" className="cursor-pointer">
											Published
										</SelectItem>
									</SelectContent>
								</Select>
							</div>
						</div>
					</ScrollArea>
				</div>
			</div>
		</div>
	);
}
