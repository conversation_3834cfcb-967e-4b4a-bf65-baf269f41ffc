import { eq } from "drizzle-orm";
import { NextResponse } from "next/server";
import { z } from "zod";
import { getDB } from "@/server/db/db-client.server";
import { deleteValue } from "@/server/kv/redis-upstash.server";
import { checkAuthAdmin } from "@/server/auth/check-auth-admin";
import { getKVKeyChangelogHeads } from "@/lib/utils";
import { changelogTranslationSchema, changelogSchema } from "@/server/db/schema.server";

// Schema for validating changelog item creation/update requests
const changelogItemSchema = z.object({
	changelogItemId: z.number().int().optional(), // For updates
	changelogId: z.number().int().min(1, "Changelog ID is required"),
	title: z.string().trim().min(1, "Title is required"),
	lang: z.string().trim().min(1, "Language is required"),
	html: z.string().trim().min(1, "Content is required"),
	status: z.number().int().min(0).max(1, "Status must be 0 (draft) or 1 (published)"),
});

export async function POST(req: Request) {
	if (!checkAuthAdmin()) {
		return NextResponse.json({ status: 401, message: "Not authorized." });
	}

	try {
		const body = await req.json();
		if (process.env.NODE_ENV === "development") {
			console.log("changelog-item body:", body);
		}

		const validatedData = changelogItemSchema.parse(body);
		const db = getDB();

		// Check if the parent changelog exists
		const parentChangelog = await db.query.changelogSchema.findFirst({
			where: eq(changelogSchema.id, validatedData.changelogId),
		});
		if (!parentChangelog) {
			return NextResponse.json({
				status: 404,
				message: "Parent changelog not found.",
			});
		}

		if (validatedData.changelogItemId) {
			// Update existing changelog translation
			const updatedTranslation = await db
				.update(changelogTranslationSchema)
				.set({
					title: validatedData.title,
					html: validatedData.html,
					status: validatedData.status,
					lang: validatedData.lang,
				})
				.where(eq(changelogTranslationSchema.id, validatedData.changelogItemId))
				.returning({ id: changelogTranslationSchema.id });

			// Clear cache for this language
			await deleteValue(getKVKeyChangelogHeads(validatedData.lang, 1));

			return NextResponse.json({
				status: 200,
				message: "Changelog translation updated successfully",
				changelogItemId: updatedTranslation[0].id,
			});
		} else {
			// Create new changelog translation
			const newTranslation = await db
				.insert(changelogTranslationSchema)
				.values({
					changelogId: validatedData.changelogId,
					title: validatedData.title,
					lang: validatedData.lang,
					html: validatedData.html,
					status: validatedData.status,
				})
				.onConflictDoNothing({
					target: [changelogTranslationSchema.changelogId, changelogTranslationSchema.lang],
				})
				.returning({ id: changelogTranslationSchema.id });

			if (newTranslation.length === 0) {
				return NextResponse.json({
					status: 400,
					message: "Changelog translation already exists.",
				});
			}

			// Clear cache for this language
			await deleteValue(getKVKeyChangelogHeads(validatedData.lang, 1));

			return NextResponse.json({
				status: 200,
				message: "Changelog translation created successfully",
				changelogItemId: newTranslation[0].id,
			});
		}
	} catch (error) {
		console.error("Error processing changelog item request:", error);

		if (error instanceof z.ZodError) {
			return NextResponse.json({
				status: 400,
				message: "Invalid request data",
				errors: error.errors,
			});
		}

		return NextResponse.json({
			status: 500,
			message: "Internal server error",
		});
	}
}
