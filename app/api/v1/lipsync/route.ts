import { getSessionUserId } from "@/server/auth/auth-session";
import { NextResponse } from "next/server";
import { getDB } from "@/server/db/db-client.server";
import { MEDIA_FILE_DURATION_LIMIT, MEDIA_FILE_DURATION_LIMIT_FREE, MEDIA_FILE_DURATION_LIMIT_STARTER, WEBNAME } from "@/lib/constants";
import { mediaTaskSchema } from "@/server/db/schema.server";
import { MediaHeadType, MediaResultStatus, MediaTaskToolType } from "@/@types/media/media-type";
import { checkUserCredit, updateUserCredit } from "@/server/utils-credits.server";
import { handleApiError } from "@/@types/error-api";
import { mixpanelTrackEvent } from "@/server/mixpanel.server";
import { EVENT_GEN_LIPSYNC } from "@/lib/track-events";
import { genSyncLipsync1_9FromFal, genSyncLipsync2FromFal, genSyncLipsync2ProFromFal } from "@/server/ai/lipsync.server";

const modelConfigs = {
	fast: {
		creditsPerSecond: 2,
		requiresPaid: false,
		genFunction: genSyncLipsync1_9FromFal,
	},
	pro: {
		creditsPerSecond: 5,
		requiresPaid: true,
		genFunction: genSyncLipsync2FromFal,
	},
	ultra: {
		creditsPerSecond: 8,
		requiresPaid: true,
		genFunction: genSyncLipsync2ProFromFal,
	},
};
import { parseMedia } from "@remotion/media-parser";
import { getUserRealtime } from "@/server/utils-user.server";
import { userHasPaid } from "@/lib/utils-user";
import { MembershipID } from "@/@types/membership-type";

type Params = {
	video: string;
	audio: string;
	model: string;
};

const checkDuration = (duration: number, isHasPaid: boolean, membershipId: MembershipID, mediaType: "video" | "audio") => {
	if (!isHasPaid && duration > MEDIA_FILE_DURATION_LIMIT_FREE) {
		return {
			valid: false,
			message: `Free plan only supports ${mediaType === "video" ? "video" : "audio"} up to ${MEDIA_FILE_DURATION_LIMIT_FREE} seconds.`,
		};
	}
	if (membershipId === MembershipID.Starter && duration > MEDIA_FILE_DURATION_LIMIT_STARTER) {
		return {
			valid: false,
			message: `Starter plan only supports ${mediaType === "video" ? "video" : "audio"} up to ${MEDIA_FILE_DURATION_LIMIT_STARTER} seconds.`,
		};
	}
	if (duration > MEDIA_FILE_DURATION_LIMIT) {
		return {
			valid: false,
			message: `${mediaType === "video" ? "Video" : "Audio"} is too long. Please upload a ${mediaType === "video" ? "video" : "audio"} up to 5 minutes.`,
		};
	}
	return {
		valid: true,
		message: "",
	};
};

export async function POST(req: Request) {
	const cfIpCountryCode = req.headers.get("cf-ipcountry");
	const cfIp = req.headers.get("cf-connecting-ip");
	const params: Params = await req.json();
	if (process.env.NODE_ENV === "development") {
		console.log("params: ", params);
	}
	if (!params.video || !params.audio) {
		return NextResponse.json({ status: 400, message: "Parameters is missing." });
	}

	try {
		const userId = await getSessionUserId();
		const user = await getUserRealtime(userId);
		const isHasPaid = userHasPaid(user!.membershipId, user!.creditOneTimeEndsAt);

		// Validate and set model
		const requestedModel = params.model || "fast";
		const modelConfig = modelConfigs[requestedModel as keyof typeof modelConfigs];
		if (!modelConfig) {
			return NextResponse.json({ status: 400, message: "Invalid model selected." });
		}

		// Check if user has access to the requested model
		if (modelConfig.requiresPaid && !isHasPaid) {
			return NextResponse.json({ status: 1001, message: "This model requires a paid plan." });
		}

		// check video duration
		const videoResult = await parseMedia({
			src: params.video,
			fields: {
				durationInSeconds: true,
			},
		});
		if (process.env.NODE_ENV === "development") {
			console.log("videoDuration: ", videoResult.durationInSeconds);
		}
		const { valid: videoDurationValid, message: videoDurationMessage } = checkDuration(
			videoResult.durationInSeconds!,
			isHasPaid,
			user!.membershipId,
			"video",
		);
		if (!videoDurationValid) {
			return NextResponse.json({ status: 1001, message: videoDurationMessage });
		}
		// check audio duration
		const audioResult = await parseMedia({
			src: params.audio,
			fields: {
				durationInSeconds: true,
			},
		});
		if (process.env.NODE_ENV === "development") {
			console.log("audioDuration: ", audioResult.durationInSeconds);
		}
		const { valid: audioDurationValid, message: audioDurationMessage } = checkDuration(
			audioResult.durationInSeconds!,
			isHasPaid,
			user!.membershipId,
			"audio",
		);
		if (!audioDurationValid) {
			return NextResponse.json({ status: 1001, message: audioDurationMessage });
		}

		const needCredits = Math.floor(audioResult.durationInSeconds!) * modelConfig.creditsPerSecond;
		const { creditConsumes, membershipLevel } = await checkUserCredit(userId, {
			needCredits: needCredits,
			existUser: user!,
		});
		if (process.env.NODE_ENV === "development") {
			console.log("creditConsumes: ", creditConsumes);
		}

		// track mixpanel event
		mixpanelTrackEvent(EVENT_GEN_LIPSYNC, userId, {
			mp_country_code: cfIpCountryCode,
			membershipLevel: membershipLevel,
		});

		let requestId: string;
		requestId = await modelConfig.genFunction(params.video, params.audio);

		// save to db
		const db = getDB();
		await db.insert(mediaTaskSchema).values({
			userId: userId,
			mediaType: MediaHeadType.Video,
			tool: MediaTaskToolType.LIPSYNC_VIDEO_WITH_AUDIO,
			model: requestedModel,
			requestBody: JSON.stringify({
				videoUrl: params.video,
				audioUrl: params.audio,
			}),
			thirdRequestId: requestId,
			status: MediaResultStatus.InProgress,
			creditsSources: JSON.stringify(creditConsumes),
			ip: cfIp,
		});
		// //更新用户token
		await updateUserCredit(userId, creditConsumes, {
			existUser: user!,
			remark: `Video task request id: ${requestId}.`,
		});

		return NextResponse.json({ message: "Success", task_status: MediaResultStatus.InProgress, request_id: requestId });
	} catch (error: any) {
		return handleApiError(error, `${WEBNAME} - /api/v1/lipsync`);
	}
}
