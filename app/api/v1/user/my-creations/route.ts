import { getSessionUserId } from "@/server/auth/auth-session";
import { NextResponse } from "next/server";
import { handleApiError } from "@/@types/error-api";
import { OSS_URL_HOST, WEBNAME } from "@/lib/constants";
import { getDB } from "@/server/db/db-client.server";
import { and, desc, eq, gt } from "drizzle-orm";
import { mediaHeadSchema, mediaTaskSchema } from "@/server/db/schema.server";
import { subDays } from "date-fns";
import { MediaResultStatus } from "@/@types/media/media-type";

export async function POST(req: Request) {
	try {
		const userId = await getSessionUserId();

		const db = getDB();

		// 获取进行中的任务
		// const mediaTasks = await db
		// 	.select()
		// 	.from(mediaTaskSchema)
		// 	.where(and(eq(mediaTaskSchema.userId, userId), eq(mediaTaskSchema.status, MediaResultStatus.InProgress)))
		// 	.orderBy(desc(mediaTaskSchema.id));

		// 获取最近10个任务（包括所有状态）
		const recentTasks = await db
			.select({
				id: mediaTaskSchema.id,
				tool: mediaTaskSchema.tool,
				status: mediaTaskSchema.status,
				createdAt: mediaTaskSchema.createdAt,
				updatedAt: mediaTaskSchema.updatedAt,
				mediaType: mediaTaskSchema.mediaType,
				model: mediaTaskSchema.model,
				error: mediaTaskSchema.error,
			})
			.from(mediaTaskSchema)
			.where(eq(mediaTaskSchema.userId, userId))
			.orderBy(desc(mediaTaskSchema.id))
			.limit(10);

		const mediaResults = await db
			.select({ uid: mediaHeadSchema.uid, filePath: mediaHeadSchema.mediaPath, type: mediaHeadSchema.mediaType })
			.from(mediaHeadSchema)
			.where(and(eq(mediaHeadSchema.userId, userId), gt(mediaHeadSchema.createdAt, subDays(new Date(), 30))))
			.orderBy(desc(mediaHeadSchema.id));
		const mediaItems = mediaResults
			.filter((result) => result.filePath)
			.map((result) => ({
				uid: result.uid,
				url: `${OSS_URL_HOST}${result.filePath}`,
				type: result.type,
			}));

		return NextResponse.json({ status: 200, message: "success", mediaItems, recentTasks });
	} catch (error: any) {
		return handleApiError(error, `${WEBNAME} - /api/v1/user/my-creations`);
	}
}
