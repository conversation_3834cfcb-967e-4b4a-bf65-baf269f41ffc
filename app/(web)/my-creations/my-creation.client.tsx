"use client";

import { useEffect, useState } from "react";
import { useSignInBoxOpenStore } from "@/store/useSignInBoxOpenStore";
import { ofetch } from "ofetch";
import { AuthError, handleError } from "@/@types/error";
import { toast } from "sonner";
import { Skeleton } from "@/components/ui/skeleton";
import { MediaHeadType, MediaResultStatus, getMediaResultStatusFormatted } from "@/@types/media/media-type";
import { BoxIcon, RefreshCcwIcon, ClockIcon, CheckCircleIcon, XCircleIcon, AlertCircleIcon } from "lucide-react";
import { PrefetchLink } from "@/components/ui/custom/prefetch-link";
import { Button, buttonVariants } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { format } from "date-fns";

export default function MyCreation({ isHasPaid }: { isHasPaid: boolean }) {
	const { setSignInBoxOpen } = useSignInBoxOpenStore();

	// ======== Display media ========
	// Pagination
	// const [page, setPage] = useState<number>(1);
	// const [totalPage, setTotalPage] = useState<number>(1);
	// const [totalCount, setTotalCount] = useState<number>(0);
	// const pageSize = 20;
	// Fetch media
	const [mediaItems, setMediaItems] = useState<any[]>([]);
	const [taskProgressNumber, setTaskProgressNumber] = useState<number>(0);
	const [recentTasks, setRecentTasks] = useState<any[]>([]);
	const [isLoading, setIsLoading] = useState(false);
	const fetchMediaItems = async () => {
		try {
			setIsLoading(true);
			const { status, message, mediaItems, recentTasks } = await ofetch(
				"/api/v1/user/my-creations",

				{
					method: "POST",
				},
			);
			handleError(status, message);
			setTaskProgressNumber(recentTasks.filter((task: any) => task.status === MediaResultStatus.InProgress).length);
			setMediaItems(mediaItems as { uid: string; url: string; type: MediaHeadType }[]);
			setRecentTasks(recentTasks || []);
			// if (totalPage === 0) {
			// 	setTotalPage(1);
			// } else {
			// 	setTotalPage(totalPage);
			// }
			// setTotalCount(total);
		} catch (error: any) {
			console.error("Failed to fetch:", error);
			if (error instanceof AuthError) {
				setSignInBoxOpen(true);
				return;
			}
			toast.error("Failed to fetch my creations.");
		} finally {
			setIsLoading(false);
		}
	};
	useEffect(() => {
		if (!isHasPaid) return;
		fetchMediaItems();
	}, []);

	return (
		<div className="container flex h-full min-h-screen w-full flex-col px-6 py-4">
			<div className="flex w-full flex-row items-center justify-between gap-2 pt-4 pb-2">
				<div className="mx-auto flex w-full flex-col">
					<h1 className="text-2xl font-medium whitespace-nowrap text-zinc-200">My creations</h1>
					<p className="text-sm text-zinc-400">Showing your creations from the last 30 days</p>
				</div>
				<Button size="lg" variant="secondary" disabled={isLoading} className="text-zinc-300" onClick={() => fetchMediaItems()}>
					<RefreshCcwIcon className={`${isLoading ? "animate-spin" : ""}`} /> Refresh
				</Button>
			</div>

			<Tabs defaultValue="creations" className="w-full">
				<TabsList className="my-2">
					<TabsTrigger value="creations" className="flex items-center gap-2 border-none dark:data-[state=active]:bg-zinc-900">
						<BoxIcon className="size-4" />
						Creations
					</TabsTrigger>
					<TabsTrigger value="tasks" className="flex items-center gap-2 border-none dark:data-[state=active]:bg-zinc-900">
						<ClockIcon className="size-4" />
						Recent Tasks
					</TabsTrigger>
				</TabsList>

				<TabsContent value="creations" className="">
					{/* {!isHasPaid && (
						<div className="bg-muted mx-auto mt-20 flex max-w-[360px] flex-col items-center gap-4 rounded-lg border border-zinc-700 p-6 shadow-xs">
							<p className="text-muted-foreground">Upgrade to view your creations</p>
							<Button onClick={() => setPlanBoxOpen(true)} variant="outline" className="">
								✨ Upgrade Now
							</Button>
						</div>
					)} */}

					{!isLoading && mediaItems.length === 0 && (
						<div className="mt-[120px] flex flex-col items-center gap-4 px-4 py-3">
							<BoxIcon className="size-16 text-zinc-600" />
							<p className="text-xl text-zinc-400">No generation history</p>
							<PrefetchLink
								href="/"
								className={cn(buttonVariants({ size: "lg", variant: "secondary" }), "bg-brand-success hover:bg-brand-success/80")}
							>
								Get Started
							</PrefetchLink>
						</div>
					)}

					<div className="mx-auto grid w-full grid-cols-1 gap-2 sm:grid-cols-2 md:grid-cols-4">
						{isLoading ? (
							<>
								{Array.from({ length: 12 }).map((_, index) => (
									<div key={index} className="group relative aspect-square w-full rounded border bg-zinc-900">
										<Skeleton className="aspect-square h-full w-full rounded bg-zinc-700/50 object-contain" />
									</div>
								))}
							</>
						) : (
							mediaItems.map((item, index) => (
								<div key={index} className="group relative aspect-square w-full rounded border border-zinc-700 bg-zinc-800">
									<video className="h-full w-full overflow-hidden rounded" controls preload="metadata">
										<source src={item.url} />
										Your browser does not support the video tag.
									</video>
								</div>
							))
						)}
					</div>
				</TabsContent>

				<TabsContent value="tasks" className="">
					{!isLoading && taskProgressNumber > 0 && (
						<div className="mb-4 flex flex-col items-center gap-4 rounded-md bg-zinc-900 px-4 py-3">
							<p className="text-zinc-200">
								You have <span className="text-yellow-500">{taskProgressNumber} task(s)</span> in progress
							</p>
						</div>
					)}

					{!isLoading && recentTasks.length === 0 && (
						<div className="mt-[120px] flex flex-col items-center gap-4 px-4 py-3">
							<ClockIcon className="size-16 text-zinc-600" />
							<p className="text-xl text-zinc-400">No recent tasks</p>
						</div>
					)}

					<div className="space-y-3">
						{isLoading ? (
							<>
								{Array.from({ length: 5 }).map((_, index) => (
									<div key={index} className="rounded-lg border border-zinc-700 bg-zinc-800 p-4">
										<Skeleton className="mb-2 h-4 w-1/3 bg-zinc-700/50" />
										<Skeleton className="mb-1 h-3 w-1/2 bg-zinc-700/50" />
										<Skeleton className="h-3 w-1/4 bg-zinc-700/50" />
									</div>
								))}
							</>
						) : (
							<>
								{recentTasks.map((task, index) => (
									<div key={index} className="rounded-lg border border-zinc-700 bg-zinc-800 p-4">
										<div className="mb-2 flex items-center justify-between">
											<div className="flex items-center gap-2">
												{task.status === MediaResultStatus.Completed && <CheckCircleIcon className="size-4 text-green-500" />}
												{task.status === MediaResultStatus.InProgress && <ClockIcon className="size-4 animate-pulse text-yellow-500" />}
												{task.status === MediaResultStatus.Failed && <XCircleIcon className="size-4 text-red-500" />}
												{(task.status === MediaResultStatus.Queued ||
													task.status === MediaResultStatus.PreparingFile ||
													task.status === MediaResultStatus.UploadingFile) && <AlertCircleIcon className="size-4 text-blue-500" />}
												<span className="font-medium text-zinc-200">{task.tool || "Unknown Tool"}</span>
											</div>
											<span
												className={cn(
													"rounded-full px-2 py-1 text-xs font-medium",
													task.status === MediaResultStatus.Completed && "bg-green-500/20 text-green-400",
													task.status === MediaResultStatus.InProgress && "bg-yellow-500/20 text-yellow-400",
													task.status === MediaResultStatus.Failed && "bg-red-500/20 text-red-400",
													(task.status === MediaResultStatus.Queued ||
														task.status === MediaResultStatus.PreparingFile ||
														task.status === MediaResultStatus.UploadingFile) &&
														"bg-blue-500/20 text-blue-400",
												)}
											>
												{getMediaResultStatusFormatted(task.status)}
											</span>
										</div>
										<div className="space-y-1 text-sm text-zinc-400">
											{/* <p>Type: {task.mediaType || "Unknown"}</p>
										<p>Model: {task.model || "Unknown"}</p> */}
											<p>Created: {format(new Date(task.createdAt), "MMM dd, yyyy HH:mm")}</p>
											{task.error && <p className="text-red-400">Error: {task.error}</p>}
										</div>
									</div>
								))}
								{recentTasks.length >= 10 && <p className="text-zinc-400">Showing only the 10 most recent tasks</p>}
							</>
						)}
					</div>
				</TabsContent>
			</Tabs>
		</div>
	);
}
