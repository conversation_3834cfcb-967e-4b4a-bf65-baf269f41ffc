import { format } from "date-fns";
import { Badge } from "@/components/ui/badge";
import { NoPrefetchLink } from "@/components/ui/custom/no-prefetch-link";

export type BlogItem = {
	slug: string;
	lang: string;
	title: string;
	intro: string;
	createdAt: number;
	image?: string;
	tags?: {
		name: string;
		slug: string;
	}[];
};

export default function BlogItems({ blogHeads, blogCategories }: { blogHeads: any[]; blogCategories: any[] }) {
	return (
		<div className="space-y-8">
			{blogHeads.map((blogHead, index) => (
				<div key={index} className="flex flex-col gap-4 py-4 sm:flex-row sm:gap-6 md:items-center">
					{blogHead.image && (
						<div className="aspect-[16/9] w-full shrink-0 overflow-hidden sm:h-full sm:max-w-[280px] md:max-w-[320px]">
							<img src={blogHead.image} alt={blogHead.title} className="h-full w-full rounded-md object-cover" loading="lazy" />
						</div>
					)}
					<div className="flex flex-col gap-2">
						{blogHead.categoryId && (
							<Badge
								variant="outline"
								className="border-brand-success bg-brand-success/15 text-brand-success rounded-full font-normal shadow-none"
							>
								{blogCategories.find((category) => category.id === blogHead.categoryId)?.name}
							</Badge>
						)}
						<NoPrefetchLink href={`/blog/${blogHead.slug}`} className="w-full space-y-2">
							<h2 className="text-primary line-clamp-2 text-xl font-semibold">{blogHead.title}</h2>
							<p className="text-muted-foreground line-clamp-2">{blogHead.intro}</p>
						</NoPrefetchLink>
						<div className="text-muted-foreground mt-2 flex items-center space-x-2 text-sm">
							<span className="text-xs">{format(blogHead.publishedAt, "MMM d, yyyy")}</span>
						</div>
					</div>
				</div>
			))}
		</div>
	);
}
