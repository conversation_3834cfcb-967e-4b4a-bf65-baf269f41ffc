services:
    fastlipsync:
        image: ghcr.io/notlandingstudio/fastlipsync-next:1.1.7
        container_name: fastlipsync-next
        build:
            context: .
            dockerfile: Dockerfile
            platforms:
                - linux/amd64/v2
        restart: always
        ports:
            - 3000:3000
        environment:
            NODE_ENV: production
            AUTH_SECRET: ${AUTH_SECRET}
            AUTH_GOOGLE_ID: ${AUTH_GOOGLE_ID}
            AUTH_GOOGLE_SECRET: ${AUTH_GOOGLE_SECRET}
            DATABASE_URL: ${DATABASE_URL}
            DATABASE_AUTH_TOKEN: ${DATABASE_AUTH_TOKEN}
            UPSTASH_REDIS_REST_URL: ${UPSTASH_REDIS_REST_URL}
            UPSTASH_REDIS_REST_TOKEN: ${UPSTASH_REDIS_REST_TOKEN}
            CLOUDFLARE_ACCOUNT_ID: ${CLOUDFLARE_ACCOUNT_ID}
            CLOUDFLARE_R2_ACCESS_KEY_ID: ${CLOUDFLARE_R2_ACCESS_KEY_ID}
            CLOUDFLARE_R2_SECRET_ACCESS_KEY: ${CLOUDFLARE_R2_SECRET_ACCESS_KEY}
            FAL_API_KEY: ${FAL_API_KEY}
            WAVESPEED_API_KEY: ${WAVESPEED_API_KEY}
            POLAR_ACCESS_TOKEN: ${POLAR_ACCESS_TOKEN}
            POLAR_WEBHOOK_SECRET: ${POLAR_WEBHOOK_SECRET}
