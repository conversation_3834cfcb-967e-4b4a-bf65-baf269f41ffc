import { ChevronRight, CircleIcon, DotIcon, type LucideIcon } from "lucide-react";
import { cn } from "@/lib/utils";
import { ComponentProps } from "react";

export function LipsyncVideoDemo({
	title,
	description,
	demos,
	className,
	...props
}: {
	title?: string;
	description?: string;
	demos: {
		before: string;
		after: string;
	}[];
} & ComponentProps<"div">) {
	return (
		<div className={cn("py-20", className)}>
			<div className="container flex flex-col items-center gap-12 px-6" {...props}>
				{title && (
					<div className="text-center">
						<h2 className="text-[32px] font-semibold text-pretty">{title}</h2>
						{description && <p className="text-muted-foreground mx-auto mt-4 max-w-4xl text-pretty">{description}</p>}
					</div>
				)}

				<div className="grid grid-cols-1 gap-6 rounded-xl border bg-zinc-900 p-4 sm:gap-8 md:grid-cols-2 md:gap-12 md:p-8">
					{demos.map((demo, index) => (
						<div
							key={index}
							className="mx-auto flex h-full max-h-[380px] flex-row items-center justify-center gap-2 rounded-xl text-zinc-300 transition-all duration-300 md:gap-4"
						>
							<div className="relative h-full">
								<video className="h-full overflow-hidden rounded-lg" controls preload="metadata">
									<source src={demo.before} />
									Your browser does not support the video tag.
								</video>
								<p className="absolute top-2 right-2 flex flex-row items-center gap-1 rounded-full bg-zinc-900/20 px-1.5 py-0.5 text-xs text-zinc-50">
									<CircleIcon className="size-2.5 fill-current text-yellow-500" />
									Original
								</p>
							</div>
							<ChevronRight className="text-brand-success size-6 shrink-0" />
							<div className="relative h-full">
								<video className="h-full overflow-hidden rounded-lg" controls preload="metadata">
									<source src={demo.after} />
									Your browser does not support the video tag.
								</video>
								<p className="absolute top-2 right-2 flex flex-row items-center gap-1 rounded-full bg-zinc-900/20 px-1.5 py-0.5 text-xs text-zinc-50">
									<CircleIcon className="text-brand-success size-2.5 fill-current" />
									Generated
								</p>
							</div>
						</div>
					))}
				</div>
			</div>
		</div>
	);
}
