import { MembershipID, membershipMapping, MembershipPeriodMonth, MembershipPeriodType, MembershipPeriodYear, MembershipType } from "@/@types/membership-type";

// ======================order======================
// product dev
const ORDER_DEV_PRODUCT_ID_1 = "772883";
const ORDER_DEV_PRODUCT_ID_2 = "772884";
const ORDER_DEV_PRODUCT_ID_3 = "772885";
// product prod
const ORDER_PROD_PRODUCT_ID_1 = "772956";
const ORDER_PROD_PRODUCT_ID_2 = "772957";
const ORDER_PROD_PRODUCT_ID_3 = "772958";

export const ORDER_PRODUCT_ID_1 = process.env.NODE_ENV === "production" ? ORDER_PROD_PRODUCT_ID_1 : ORDER_DEV_PRODUCT_ID_1;
export const ORDER_PRODUCT_ID_2 = process.env.NODE_ENV === "production" ? ORDER_PROD_PRODUCT_ID_2 : ORDER_DEV_PRODUCT_ID_2;
export const ORDER_PRODUCT_ID_3 = process.env.NODE_ENV === "production" ? ORDER_PROD_PRODUCT_ID_3 : ORDER_DEV_PRODUCT_ID_3;

export interface OrderInfo {
	productId: string;
	membership: MembershipType;
	credits: number;
}

const orderVariants: OrderInfo[] = [
	{
		productId: ORDER_PRODUCT_ID_1,
		membership: membershipMapping[MembershipID.Pro],
		credits: 200,
	},
	{
		productId: ORDER_PRODUCT_ID_2,
		membership: membershipMapping[MembershipID.Pro],
		credits: 650,
	},

	{
		productId: ORDER_PRODUCT_ID_3,
		membership: membershipMapping[MembershipID.Pro],
		credits: 1600,
	},
];
export function getOrderProductInfo(productId: string | number): OrderInfo | undefined {
	if (typeof productId === "number") {
		productId = productId.toString();
	}
	return orderVariants.find((v) => v.productId === productId);
}

export enum OrderSource {
	Polar = "polar",
	Cream = "cream",
	Lmsqueezy = "lemonsqueezy",
}

// pending, paid, refunded, partially_refunded
export enum OrderStatus {
	Pending = "pending",
	Paid = "paid",
	Refunded = "refunded",
	PartiallyRefunded = "partially_refunded",
}
export function getOrderStatusName(status: OrderStatus): string {
	switch (status) {
		case OrderStatus.Pending:
			return "Pending";
		case OrderStatus.Paid:
			return "Paid";
		case OrderStatus.Refunded:
			return "Refunded";
		case OrderStatus.PartiallyRefunded:
			return "Partially Refunded";
		default:
			return "Unknown";
	}
}

// ======================Top up Order=====================
export interface TopUpOrderInfo {
	productId: string;
	credits: number;
	price: number;
	currency: string;
}
export const topUpOrderVariants: TopUpOrderInfo[] = [
	{
		productId: "8adc77ea-0a3f-42e0-b2e2-469df8015a73",
		credits: 500,
		price: 10,
		currency: "$",
	},
	{
		productId: "3613e741-2a1a-4c6a-a10a-0535cc9c2205",
		credits: 1000,
		price: 20,
		currency: "$",
	},

	{
		productId: "97403f84-5ac7-4789-a75e-17136999c062",
		credits: 3000,
		price: 60,
		currency: "$",
	},
];
export function getTopUpOrderProductInfo(productId: string | number): TopUpOrderInfo | undefined {
	if (typeof productId === "number") {
		productId = productId.toString();
	}
	return topUpOrderVariants.find((v) => v.productId === productId);
}

// ======================Subscription Polar=====================
// incomplete, incomplete_expired, trialing, active, past_due, canceled, unpaid
export enum SubscriptionStatus {
	Incomplete = "incomplete",
	IncompleteExpired = "incomplete_expired",
	Trialing = "trialing",
	Active = "active",
	PastDue = "past_due",
	Canceled = "canceled",
	Unpaid = "unpaid",
}
export function getSubscriptionStatusName(status: SubscriptionStatus): string {
	switch (status) {
		case SubscriptionStatus.Incomplete:
			return "Incomplete";
		case SubscriptionStatus.IncompleteExpired:
			return "Incomplete Expired";
		case SubscriptionStatus.Trialing:
			return "Trialing";
		case SubscriptionStatus.Active:
			return "Active";
		case SubscriptionStatus.PastDue:
			return "Past Due";
		case SubscriptionStatus.Canceled:
			return "Canceled";
		case SubscriptionStatus.Unpaid:
			return "Unpaid";
		default:
			return "Unknown";
	}
}
// product dev
const SUBSCRIPTION_DEV_PRODUCT_ID_MONTH_STARTER = "5800929d-f8a5-4046-8971-79f14cecd187";
const SUBSCRIPTION_DEV_PRODUCT_ID_YEAR_STARTER = "cc4705e1-4481-4e19-8f5f-4b658e9ef29d";
const SUBSCRIPTION_DEV_PRODUCT_ID_MONTH_PRO = "pro-monthly";
const SUBSCRIPTION_DEV_PRODUCT_ID_YEAR_PRO = "pro-yearly";
const SUBSCRIPTION_DEV_PRODUCT_ID_MONTH_PREMIUM = "premium-monthly";
const SUBSCRIPTION_DEV_PRODUCT_ID_YEAR_PREMIUM = "premium-yearly";
// product prod
const SUBSCRIPTION_PROD_PRODUCT_ID_MONTH_STARTER = "0c906d6f-f78a-4af6-b9d1-20df1221cc88";
const SUBSCRIPTION_PROD_PRODUCT_ID_YEAR_STARTER = "5aa237a4-6f60-4265-a350-df513ae30af4";
const SUBSCRIPTION_PROD_PRODUCT_ID_MONTH_PRO = "16708d44-e2d4-45fe-83a5-f3445ec915dc";
const SUBSCRIPTION_PROD_PRODUCT_ID_YEAR_PRO = "7d20416d-235f-47df-bc0a-4eb8ce613d3a";
const SUBSCRIPTION_PROD_PRODUCT_ID_MONTH_PREMIUM = "7c4a0f8e-77b5-4320-9b35-219c8ab7a447";
const SUBSCRIPTION_PROD_PRODUCT_ID_YEAR_PREMIUM = "5b3236ee-6a00-4115-925f-1b47d1d8eced";

export const SUBSCRIPTION_PRODUCT_ID_MONTH_STARTER =
	process.env.NODE_ENV === "production" ? SUBSCRIPTION_PROD_PRODUCT_ID_MONTH_STARTER : SUBSCRIPTION_DEV_PRODUCT_ID_MONTH_STARTER;
export const SUBSCRIPTION_PRODUCT_ID_YEAR_STARTER =
	process.env.NODE_ENV === "production" ? SUBSCRIPTION_PROD_PRODUCT_ID_YEAR_STARTER : SUBSCRIPTION_DEV_PRODUCT_ID_YEAR_STARTER;
export const SUBSCRIPTION_PRODUCT_ID_MONTH_PRO =
	process.env.NODE_ENV === "production" ? SUBSCRIPTION_PROD_PRODUCT_ID_MONTH_PRO : SUBSCRIPTION_DEV_PRODUCT_ID_MONTH_PRO;
export const SUBSCRIPTION_PRODUCT_ID_YEAR_PRO =
	process.env.NODE_ENV === "production" ? SUBSCRIPTION_PROD_PRODUCT_ID_YEAR_PRO : SUBSCRIPTION_DEV_PRODUCT_ID_YEAR_PRO;
export const SUBSCRIPTION_PRODUCT_ID_MONTH_PREMIUM =
	process.env.NODE_ENV === "production" ? SUBSCRIPTION_PROD_PRODUCT_ID_MONTH_PREMIUM : SUBSCRIPTION_DEV_PRODUCT_ID_MONTH_PREMIUM;
export const SUBSCRIPTION_PRODUCT_ID_YEAR_PREMIUM =
	process.env.NODE_ENV === "production" ? SUBSCRIPTION_PROD_PRODUCT_ID_YEAR_PREMIUM : SUBSCRIPTION_DEV_PRODUCT_ID_YEAR_PREMIUM;

export interface MembershipInfo {
	productId: string;
	membership: MembershipType;
	period: MembershipPeriodType;
}

const membershipVariants: MembershipInfo[] = [
	{
		productId: SUBSCRIPTION_PRODUCT_ID_MONTH_STARTER,
		membership: membershipMapping[MembershipID.Starter],
		period: MembershipPeriodMonth,
	},
	{
		productId: SUBSCRIPTION_PRODUCT_ID_YEAR_STARTER,
		membership: membershipMapping[MembershipID.Starter],
		period: MembershipPeriodYear,
	},
	{
		productId: SUBSCRIPTION_PRODUCT_ID_MONTH_PRO,
		membership: membershipMapping[MembershipID.Pro],
		period: MembershipPeriodMonth,
	},
	{
		productId: SUBSCRIPTION_PRODUCT_ID_YEAR_PRO,
		membership: membershipMapping[MembershipID.Pro],
		period: MembershipPeriodYear,
	},
	{
		productId: SUBSCRIPTION_PRODUCT_ID_MONTH_PREMIUM,
		membership: membershipMapping[MembershipID.Premium],
		period: MembershipPeriodMonth,
	},
	{
		productId: SUBSCRIPTION_PRODUCT_ID_YEAR_PREMIUM,
		membership: membershipMapping[MembershipID.Premium],
		period: MembershipPeriodYear,
	},
];
export function getMembershipProductInfo(productId: string | number): MembershipInfo | undefined {
	if (typeof productId === "number") {
		productId = productId.toString();
	}
	return membershipVariants.find((v) => v.productId === productId);
}

// 限制用户会员降级，但这不是最好的方案，更好的方案为：**只能在下一个收费周期进行一个会员顶级更改**
export function canChangePlan(userProductId: string | number, targetProductId: string | number): boolean {
	const userMembershipInfo = getMembershipProductInfo(userProductId);
	if (!userMembershipInfo) return true;
	const targetMembershipInfo = getMembershipProductInfo(targetProductId);
	if (!targetMembershipInfo) return true;

	// 如果当前是年订阅
	if (userMembershipInfo.period.value === MembershipPeriodYear.value) {
		// 年订阅不能更改成月订阅
		if (targetMembershipInfo.period.value === MembershipPeriodMonth.value) {
			return false;
		}
		// 年订阅不能降级
		if (targetMembershipInfo.membership.id <= userMembershipInfo.membership.id) {
			return false;
		}
		return true;
	}

	// 如果当前是月订阅,订阅不能降级
	// 目标是月订阅，只能升级
	if (targetMembershipInfo.period.value === MembershipPeriodMonth.value) {
		if (targetMembershipInfo.membership.id <= userMembershipInfo.membership.id) {
			return false;
		}
		return true;
	}
	// 目标是年订阅，升级或将当前月订阅降级为年订阅
	if (targetMembershipInfo.membership.id < userMembershipInfo.membership.id) {
		return false;
	}
	return true;
}
